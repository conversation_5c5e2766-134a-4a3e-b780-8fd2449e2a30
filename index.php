<?php
/**
 * The main template file
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 */

get_header();
?>

<main id="primary" class="site-main">
    <?php
    if (have_posts()) :
        while (have_posts()) :
            the_post();
            ?>
            <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                <header class="entry-header">
                    <?php the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>'); ?>
                    <div class="entry-meta">
                        <span class="posted-on">
                            <?php echo get_the_date(); ?>
                        </span>
                        <span class="byline">
                            <?php the_author(); ?>
                        </span>
                        <span class="categories">
                            <?php the_category(', '); ?>
                        </span>
                    </div><!-- .entry-meta -->
                </header><!-- .entry-header -->

                <?php if (has_post_thumbnail()) : ?>
                <div class="post-thumbnail">
                    <?php the_post_thumbnail('medium'); ?>
                </div>
                <?php endif; ?>

                <div class="entry-content">
                    <?php the_excerpt(); ?>
                    <a href="<?php the_permalink(); ?>" class="btn-primary read-more"><?php _e('Read More', 'eastsepik-theme'); ?></a>
                </div><!-- .entry-content -->
            </article><!-- #post-<?php the_ID(); ?> -->
            <?php
        endwhile;

        // Pagination
        the_posts_pagination(array(
            'prev_text' => '&laquo; ' . __('Previous', 'eastsepik-theme'),
            'next_text' => __('Next', 'eastsepik-theme') . ' &raquo;',
        ));

    else :
        ?>
        <p><?php _e('No posts found.', 'eastsepik-theme'); ?></p>
        <?php
    endif;
    ?>
</main><!-- #primary -->

<?php 
get_sidebar();
get_footer();
?> 