<?php
/**
 * The template for displaying all single posts
 */

get_header();
?>

<main id="primary" class="site-main">
    <?php
    while (have_posts()) :
        the_post();
        ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
            <header class="entry-header">
                <?php the_title('<h1 class="entry-title">', '</h1>'); ?>
                <div class="entry-meta">
                    <span class="posted-on">
                        <?php echo get_the_date(); ?>
                    </span>
                    <span class="byline">
                        <?php the_author(); ?>
                    </span>
                    <span class="categories">
                        <?php the_category(', '); ?>
                    </span>
                </div><!-- .entry-meta -->
            </header><!-- .entry-header -->

            <?php if (has_post_thumbnail()) : ?>
            <div class="post-thumbnail">
                <?php the_post_thumbnail('large'); ?>
            </div>
            <?php endif; ?>

            <div class="entry-content">
                <?php the_content(); ?>
                
                <?php
                wp_link_pages(array(
                    'before' => '<div class="page-links">' . __('Pages:', 'eastsepik-theme'),
                    'after'  => '</div>',
                ));
                ?>
            </div><!-- .entry-content -->

            <footer class="entry-footer">
                <?php if (has_tag()) : ?>
                <div class="tags">
                    <?php the_tags(__('Tags: ', 'eastsepik-theme'), ', ', ''); ?>
                </div>
                <?php endif; ?>
                
                <div class="post-navigation">
                    <div class="nav-previous"><?php previous_post_link('%link', '&larr; %title'); ?></div>
                    <div class="nav-next"><?php next_post_link('%link', '%title &rarr;'); ?></div>
                </div>
                
                <?php
                // If comments are open or we have at least one comment, load up the comment template.
                if (comments_open() || get_comments_number()) :
                    comments_template();
                endif;
                ?>
            </footer><!-- .entry-footer -->
        </article><!-- #post-<?php the_ID(); ?> -->
        <?php
    endwhile;
    ?>
</main><!-- #primary -->

<?php
get_sidebar();
get_footer();
?> 