<?php
/**
 * East Sepik Theme functions and definitions
 */

// Theme setup
function eastsepik_theme_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'eastsepik-theme'),
        'footer' => __('Footer Menu', 'eastsepik-theme'),
    ));
}
add_action('after_setup_theme', 'eastsepik_theme_setup');

// Enqueue scripts and styles
function eastsepik_theme_scripts() {
    // Bootstrap CSS
    wp_enqueue_style('bootstrap-css', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css', array(), '5.3.0');

    // Enqueue main stylesheet (after Bootstrap)
    wp_enqueue_style('eastsepik-style', get_stylesheet_uri(), array('bootstrap-css'), '1.0.0');

    // Google Fonts
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap', array(), null);

    // Font Awesome
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', array(), '6.4.0');

    // Bootstrap JavaScript
    wp_enqueue_script('bootstrap-js', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js', array(), '5.3.0', true);

    // Main JavaScript file
    wp_enqueue_script('jquery');
    wp_enqueue_script('eastsepik-scripts', get_template_directory_uri() . '/js/main.js', array('jquery'), '1.0.0', true);
}
add_action('wp_enqueue_scripts', 'eastsepik_theme_scripts');

// Register widget areas
function eastsepik_theme_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'eastsepik-theme'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here to appear in your sidebar.', 'eastsepik-theme'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer Widget Area', 'eastsepik-theme'),
        'id'            => 'footer-1',
        'description'   => __('Add widgets here to appear in your footer.', 'eastsepik-theme'),
        'before_widget' => '<section id="%1$s" class="footer-widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'eastsepik_theme_widgets_init');

// Add customizer options
function eastsepik_customize_register($wp_customize) {
    // Add a section for theme options
    $wp_customize->add_section('eastsepik_theme_options', array(
        'title'    => __('Theme Options', 'eastsepik-theme'),
        'priority' => 130,
    ));
    
    // Add setting for accent color
    $wp_customize->add_setting('accent_color', array(
        'default'           => '#FFD700', // Default gold color
        'sanitize_callback' => 'sanitize_hex_color',
    ));
    
    // Add control for accent color
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'accent_color', array(
        'label'    => __('Accent Color', 'eastsepik-theme'),
        'section'  => 'eastsepik_theme_options',
        'settings' => 'accent_color',
    )));
    
    // Add setting for primary color
    $wp_customize->add_setting('primary_color', array(
        'default'           => '#8B0000', // Default maroon color
        'sanitize_callback' => 'sanitize_hex_color',
    ));
    
    // Add control for primary color
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'primary_color', array(
        'label'    => __('Primary Color', 'eastsepik-theme'),
        'section'  => 'eastsepik_theme_options',
        'settings' => 'primary_color',
    )));
    
    // Add a section for hero options
    $wp_customize->add_section('eastsepik_hero_options', array(
        'title'    => __('Hero Section', 'eastsepik-theme'),
        'priority' => 120,
    ));
    
    // Hero title
    $wp_customize->add_setting('hero_title', array(
        'default'           => __('Welcome to the East Sepik Provincial Administration WP Theme', 'eastsepik-theme'),
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('hero_title', array(
        'label'    => __('Hero Title', 'eastsepik-theme'),
        'section'  => 'eastsepik_hero_options',
        'type'     => 'text',
    ));
    
    // Hero subtitle
    $wp_customize->add_setting('hero_subtitle', array(
        'default'           => __('A custom WordPress theme showcasing the colors of East Sepik Province', 'eastsepik-theme'),
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('hero_subtitle', array(
        'label'    => __('Hero Subtitle', 'eastsepik-theme'),
        'section'  => 'eastsepik_hero_options',
        'type'     => 'text',
    ));
    
    // Hero button text
    $wp_customize->add_setting('hero_button_text', array(
        'default'           => __('Learn More', 'eastsepik-theme'),
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('hero_button_text', array(
        'label'    => __('Button Text', 'eastsepik-theme'),
        'section'  => 'eastsepik_hero_options',
        'type'     => 'text',
    ));
}
add_action('customize_register', 'eastsepik_customize_register');

// Output custom CSS for customizer options
function eastsepik_customizer_css() {
    ?>
    <style type="text/css">
        :root {
            --maroon: <?php echo get_theme_mod('primary_color', '#8B0000'); ?>;
            --gold: <?php echo get_theme_mod('accent_color', '#FFD700'); ?>;
        }
        .main-navigation, .btn-secondary {
            background-color: var(--gold);
        }
        .site-header, .site-footer {
            background-color: var(--maroon);
        }
        .entry-title, .widget-title, .section-title {
            color: var(--maroon);
        }
        .section-title:after {
            background: var(--gold);
        }
    </style>
    <?php
}
add_action('wp_head', 'eastsepik_customizer_css');

/**
 * Implement custom excerpt length
 */
function eastsepik_custom_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'eastsepik_custom_excerpt_length', 999);

/**
 * Change excerpt more string
 */
function eastsepik_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'eastsepik_excerpt_more');

/**
 * Add a back to top button to the footer
 */
function eastsepik_back_to_top() {
    echo '<a href="#" class="back-to-top" style="display:none;position:fixed;bottom:20px;right:20px;background:var(--maroon);color:var(--white);width:40px;height:40px;text-align:center;line-height:40px;border-radius:50%;z-index:999;"><span class="dashicons dashicons-arrow-up-alt2"></span></a>';
}
add_action('wp_footer', 'eastsepik_back_to_top');

/**
 * Sanitize callback functions for customizer
 */
function eastsepik_sanitize_checkbox($checked) {
    return ((isset($checked) && true == $checked) ? true : false);
}

/**
 * Add dashicons to the front end
 */
function eastsepik_load_dashicons() {
    wp_enqueue_style('dashicons');
}
add_action('wp_enqueue_scripts', 'eastsepik_load_dashicons'); 