<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php
if (function_exists('wp_body_open')) {
    wp_body_open();
}
?>
<div id="page" class="site">
    <!-- Bootstrap Wavy Header -->
    <header id="masthead" class="site-header">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="site-branding text-center">
                        <?php
                        if (has_custom_logo()) :
                            the_custom_logo();
                        else :
                        ?>
                            <h1 class="site-title mb-2">
                                <a href="<?php echo esc_url(home_url('/')); ?>" rel="home" class="text-decoration-none">
                                    <?php bloginfo('name'); ?>
                                </a>
                            </h1>
                            <p class="site-description mb-0"><?php bloginfo('description'); ?></p>
                        <?php endif; ?>
                    </div><!-- .site-branding -->
                </div>
            </div>
        </div>

        <!-- Bootstrap Navbar with Wavy Design -->
        <nav class="navbar navbar-expand-lg main-navigation">
            <div class="container-fluid">
                <button class="navbar-toggler menu-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <i class="fas fa-bars"></i>
                </button>

                <div class="collapse navbar-collapse justify-content-center" id="navbarNav">
                    <?php
                    wp_nav_menu(array(
                        'theme_location' => 'primary',
                        'menu_id'        => 'primary-menu',
                        'menu_class'     => 'navbar-nav nav-menu',
                        'container'      => false,
                        'fallback_cb'    => false,
                    ));
                    ?>
                </div>
            </div>
        </nav><!-- .main-navigation -->
    </header><!-- #masthead -->

    <main id="content" class="site-content">
        <?php
        // If we're on the front page and not on the static front page
        if (is_front_page() && !is_home()) :
            get_template_part('template-parts/content', 'hero');
        endif;
        ?>
