<?php
/**
 * The template for displaying the front page
 */

get_header();
?>

<div class="hero-section">
    <div class="container">
        <h1><?php _e('Welcome to the East Sepik Provincial Administration WP Theme', 'eastsepik-theme'); ?></h1>
        <p><?php _e('A custom WordPress theme showcasing the colors of East Sepik Province', 'eastsepik-theme'); ?></p>
        <a href="#featured" class="btn-secondary"><?php _e('Learn More', 'eastsepik-theme'); ?></a>
    </div>
</div>

<main id="primary" class="site-main">
    <section id="featured" class="featured-section">
        <h2 class="section-title"><?php _e('Featured Content', 'eastsepik-theme'); ?></h2>
        <div class="featured-posts">
            <?php
            $featured_query = new WP_Query(array(
                'posts_per_page' => 3,
                'post_type' => 'post',
                'meta_key' => '_thumbnail_id', // Only posts with featured images
            ));
            
            if ($featured_query->have_posts()) :
                while ($featured_query->have_posts()) :
                    $featured_query->the_post();
                    ?>
                    <div class="featured-post">
                        <?php if (has_post_thumbnail()) : ?>
                            <a href="<?php the_permalink(); ?>" class="featured-image">
                                <?php the_post_thumbnail('medium'); ?>
                            </a>
                        <?php endif; ?>
                        <h3 class="featured-title">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h3>
                        <div class="featured-excerpt">
                            <?php the_excerpt(); ?>
                        </div>
                        <a href="<?php the_permalink(); ?>" class="btn-primary"><?php _e('Read More', 'eastsepik-theme'); ?></a>
                    </div>
                    <?php
                endwhile;
                wp_reset_postdata();
            else :
                echo '<p>' . __('No featured posts found.', 'eastsepik-theme') . '</p>';
            endif;
            ?>
        </div>
    </section>
    
    <?php
    // If the front page is set to display the blog posts index, also show latest posts
    if (get_option('show_on_front') == 'posts') :
    ?>
    <section class="latest-posts">
        <h2 class="section-title"><?php _e('Latest Posts', 'eastsepik-theme'); ?></h2>
        
        <?php
        if (have_posts()) :
            echo '<div class="posts-grid">';
            
            while (have_posts()) :
                the_post();
                ?>
                <article id="post-<?php the_ID(); ?>" <?php post_class('grid-item'); ?>>
                    <header class="entry-header">
                        <?php the_title('<h3 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h3>'); ?>
                        <div class="entry-meta">
                            <span class="posted-on"><?php echo get_the_date(); ?></span>
                        </div>
                    </header>
                    
                    <?php if (has_post_thumbnail()) : ?>
                    <a href="<?php the_permalink(); ?>" class="post-thumbnail">
                        <?php the_post_thumbnail('medium'); ?>
                    </a>
                    <?php endif; ?>
                    
                    <div class="entry-content">
                        <?php the_excerpt(); ?>
                        <a href="<?php the_permalink(); ?>" class="read-more"><?php _e('Read More', 'eastsepik-theme'); ?></a>
                    </div>
                </article>
                <?php
            endwhile;
            
            echo '</div>';
            
            the_posts_pagination();
            
        endif;
        ?>
    </section>
    <?php endif; ?>
</main>

<?php
get_footer();
?> 