/*
Theme Name: East Sepik Provincial Administration WP Theme
Theme URI: https://yourwebsite.com/eastsepik-theme
Author: Your Name
Author URI: https://yourwebsite.com
Description: A custom WordPress theme based on East Sepik Province colors
Version: 1.0
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: eastsepik-theme
*/

/* East Sepik Color Palette */
:root {
  --primary: #2196F3; /* Material Blue 500 */
  --softblue: #336699;
  --gold: #FFD700;
  --green: #006400;
  --maroon: #8B0000;
  --black: #000000;
  --white: #FFFFFF;
  --cream: #FFFDD0;
}

/* Base Styles */
body {
  font-family: 'Open Sans', sans-serif;
  color: var(--black);
  background-color: var(--white);
  margin: 0;
  padding: 0;
}

/* Header Styles */
.site-header {
  background-color: var(--softblue);
  color: var(--white);
  padding: 1rem 0;
  border-bottom: 5px solid var(--gold);
}

.site-branding {
  padding: 0 2rem;
}

.site-title {
  margin: 0;
  font-size: 2rem;
}

.site-title a {
  color: var(--white);
  text-decoration: none;
}

.site-description {
  margin: 0.5rem 0 0;
  font-style: italic;
}

/* Navigation */
.main-navigation {
  background-color: var(--gold);
  padding: 0 2rem;
}

.nav-menu {
  list-style: none;
  display: flex;
  margin: 0;
  padding: 0;
}

.nav-menu li {
  position: relative;
}

.nav-menu li a {
  display: block;
  padding: 1rem;
  color: var(--black);
  text-decoration: none;
  font-weight: 600;
  transition: background-color 0.3s, color 0.3s;
}

.nav-menu li a:hover {
  background-color: var(--green);
  color: var(--white);
}

/* Content Styles */
.site-content {
  display: flex;
  flex-wrap: wrap;
  padding: 2rem;
}

.site-main {
  flex: 1;
  min-width: 0;
  padding-right: 2rem;
}

.widget-area {
  width: 300px;
}

/* Post Styles */
article {
  margin-bottom: 3rem;
}

.entry-title {
  color: var(--softblue);
  margin-top: 0;
}

.entry-title a {
  color: var(--softblue);
  text-decoration: none;
}

.entry-title a:hover {
  color: var(--green);
}

.entry-meta {
  color: #666;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.entry-meta span {
  margin-right: 1rem;
}

.post-thumbnail {
  margin-bottom: 1.5rem;
}

.post-thumbnail img {
  max-width: 100%;
  height: auto;
}

/* Buttons */
.btn-primary, .read-more {
  display: inline-block;
  background-color: var(--green);
  color: var(--white);
  border: 2px solid var(--gold);
  padding: 0.5rem 1rem;
  text-decoration: none;
  font-weight: 600;
  border-radius: 3px;
  transition: background-color 0.3s;
}

.btn-primary:hover, .read-more:hover {
  background-color: var(--softblue);
  color: var(--white);
}

.btn-secondary {
  display: inline-block;
  background-color: var(--gold);
  color: var(--black);
  border: 2px solid var(--softblue);
  padding: 0.5rem 1rem;
  text-decoration: none;
  font-weight: 600;
  border-radius: 3px;
  transition: background-color 0.3s;
}

.btn-secondary:hover {
  background-color: var(--softblue);
  color: var(--white);
}

/* Wave Divider */
.crocodile-wave {
  height: 100px;
  background: var(--white);
  position: relative;
  overflow: hidden;
}

.crocodile-wave::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--maroon) 0%, #FF6B35 15%, #F7931E 30%, var(--gold) 45%, #9ACD32 60%, var(--green) 75%, var(--softblue) 90%, var(--maroon) 100%);
  clip-path: polygon(
    0% 100%,
    2% 85%,
    4% 90%,
    6% 75%,
    8% 85%,
    10% 70%,
    12% 80%,
    14% 65%,
    16% 75%,
    18% 60%,
    20% 70%,
    22% 55%,
    24% 65%,
    26% 50%,
    28% 60%,
    30% 45%,
    32% 55%,
    34% 40%,
    36% 50%,
    38% 35%,
    40% 45%,
    42% 30%,
    44% 40%,
    46% 25%,
    48% 35%,
    50% 20%,
    52% 30%,
    54% 25%,
    56% 35%,
    58% 30%,
    60% 40%,
    62% 35%,
    64% 45%,
    66% 40%,
    68% 50%,
    70% 45%,
    72% 55%,
    74% 50%,
    76% 60%,
    78% 55%,
    80% 65%,
    82% 60%,
    84% 70%,
    86% 65%,
    88% 75%,
    90% 70%,
    92% 80%,
    94% 75%,
    96% 85%,
    98% 80%,
    100% 90%,
    100% 100%
  );
}

/* Footer Styles */
.site-footer {
  background: linear-gradient(135deg, var(--maroon) 0%, #8B0000 25%, #A0522D 50%, var(--maroon) 100%);
  color: var(--white);
  padding: 3rem 0 1rem;
  position: relative;
  margin-top: 0;
}

.footer-widgets {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.footer-widget {
  flex: 1;
  min-width: 250px;
  margin-right: 2rem;
  margin-bottom: 1.5rem;
}

.footer-widget:last-child {
  margin-right: 0;
}

.site-info {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-menu {
  list-style: none;
  display: flex;
  justify-content: center;
  padding: 0;
  margin: 1rem 0 0;
}

.footer-menu li {
  margin: 0 1rem;
}

.footer-menu li a {
  color: var(--gold);
  text-decoration: none;
}

.footer-menu li a:hover {
  text-decoration: underline;
}

/* Widget Styles */
.widget {
  margin-bottom: 2rem;
}

.widget-title {
  color: var(--softblue);
  border-bottom: 2px solid var(--gold);
  padding-bottom: 0.5rem;
}

/* Comments */
.comments-area {
  margin-top: 3rem;
}

.comment-list {
  list-style: none;
  padding: 0;
}

.comment {
  padding: 1rem;
  margin-bottom: 1rem;
  background: #f9f9f9;
  border-left: 3px solid var(--gold);
}

/* Pagination */
.pagination {
  margin: 2rem 0;
  text-align: center;
}

.page-numbers {
  display: inline-block;
  padding: 0.5rem 1rem;
  margin: 0 0.2rem;
  border: 1px solid #ddd;
  text-decoration: none;
}

.page-numbers.current {
  background-color: var(--gold);
  color: var(--black);
  border-color: var(--gold);
}

/* Hero Section for Front Page */
.hero-section {
  background-color: var(--maroon);
  color: var(--white);
  padding: 5rem 2rem;
  text-align: center;
  margin-bottom: 3rem;
}

.hero-section h1 {
  font-size: 3rem;
  margin: 0 0 1rem;
}

.hero-section p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

/* Featured Section */
.featured-section {
  margin-bottom: 3rem;
}

.section-title {
  color: var(--maroon);
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

.section-title:after {
  content: '';
  display: block;
  width: 50px;
  height: 3px;
  background: var(--gold);
  margin: 0.5rem auto 0;
}

.featured-posts {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -1rem;
}

.featured-post {
  flex: 1;
  min-width: 250px;
  margin: 0 1rem 2rem;
  padding: 1rem;
  border: 1px solid #eee;
  border-radius: 5px;
  transition: box-shadow 0.3s;
}

.featured-post:hover {
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.featured-image {
  display: block;
  margin-bottom: 1rem;
}

.featured-image img {
  width: 100%;
  height: auto;
  border-radius: 3px;
}

.featured-title {
  margin: 0 0 1rem;
}

.featured-title a {
  color: var(--maroon);
  text-decoration: none;
}

.featured-excerpt {
  margin-bottom: 1rem;
}

/* Responsive Styles */
.menu-toggle {
  display: none;
  background-color: var(--gold);
  color: var(--black);
  border: none;
  padding: 10px 15px;
  margin: 0 auto;
  cursor: pointer;
  font-weight: bold;
}

@media screen and (max-width: 768px) {
  .site-content {
    flex-direction: column;
    padding: 1rem;
  }
  
  .site-main {
    padding-right: 0;
    margin-bottom: 2rem;
  }
  
  .widget-area {
    width: 100%;
  }
  
  .site-branding {
    text-align: center;
  }
  
  .menu-toggle {
    display: block;
  }
  
  .nav-menu {
    display: none;
    flex-direction: column;
  }
  
  .nav-menu.toggled {
    display: flex;
  }
  
  .nav-menu li {
    width: 100%;
    text-align: center;
  }
  
  .nav-menu li a {
    padding: 10px;
    width: 100%;
    display: block;
  }
  
  .featured-posts {
    flex-direction: column;
  }
  
  .footer-widgets {
    flex-direction: column;
  }
  
  .footer-widget {
    margin-right: 0;
  }
  
  .footer-menu {
    flex-direction: column;
    align-items: center;
  }
  
  .footer-menu li {
    margin: 0.5rem 0;
  }
}

@media screen and (max-width: 480px) {
  .hero-section {
    padding: 3rem 1rem;
  }
  
  .hero-section h1 {
    font-size: 2rem;
  }
  
  .entry-title {
    font-size: 1.5rem;
  }
  
  .post-navigation {
    flex-direction: column;
  }
  
  .nav-previous, .nav-next {
    margin-bottom: 10px;
  }
} 
