/**
 * East Sepik Theme JavaScript
 */
(function($) {
    'use strict';
    
    // When the DOM is fully loaded
    $(document).ready(function() {
        // Handle mobile menu toggle
        $('.menu-toggle').on('click', function() {
            $('.nav-menu').toggleClass('toggled');
            if ($(this).attr('aria-expanded') === 'true') {
                $(this).attr('aria-expanded', 'false');
            } else {
                $(this).attr('aria-expanded', 'true');
            }
        });
        
        // Smooth scroll for in-page links
        $('a[href*="#"]:not([href="#"])').click(function() {
            if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && location.hostname === this.hostname) {
                var target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 70
                    }, 1000);
                    return false;
                }
            }
        });
        
        // Add active class to current menu item
        var currentUrl = window.location.href;
        $('.nav-menu a').each(function() {
            if ($(this).attr('href') === currentUrl) {
                $(this).closest('li').addClass('current-menu-item');
            }
        });
        
        // Initialize back to top button
        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $('.back-to-top').fadeIn();
            } else {
                $('.back-to-top').fadeOut();
            }
        });

        // Back to top button click handler
        $('.back-to-top').click(function() {
            $('html, body').animate({scrollTop : 0},800);
            return false;
        });
    });
    
})(jQuery); 